﻿using Liam.Cryptography.Services;
using <PERSON>.Cryptography.Extensions;

namespace Liam.Cryptography.Example;

/// <summary>
/// Liam.Cryptography功能库示例程序
/// 演示核心加密功能的使用方法
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.Cryptography 功能库示例程序 ===");
        Console.WriteLine("本程序演示Liam.Cryptography库的核心功能");
        Console.WriteLine();

        try
        {
            // 1. 对称加密演示
            Console.WriteLine("🔐 1. 对称加密演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await SymmetricEncryptionDemo();
            Console.WriteLine();

            // 2. 非对称加密演示
            Console.WriteLine("🔑 2. 非对称加密演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await AsymmetricEncryptionDemo();
            Console.WriteLine();

            // 3. 哈希算法演示
            Console.WriteLine("🔍 3. 哈希算法演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await HashingDemo();
            Console.WriteLine();

            // 4. 数字签名演示
            Console.WriteLine("✍️ 4. 数字签名演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await DigitalSignatureDemo();
            Console.WriteLine();

            // 5. 扩展方法演示
            Console.WriteLine("🛠️ 5. 扩展方法演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            await ExtensionMethodsDemo();
            Console.WriteLine();

            Console.WriteLine("✅ 所有示例演示完成！");
            Console.WriteLine("📖 更多详细信息请参阅：https://www.nuget.org/packages/Liam.Cryptography/");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 程序执行出错: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    /// <summary>
    /// 对称加密演示
    /// </summary>
    static Task SymmetricEncryptionDemo()
    {
        try
        {
            var aes = new AesSymmetricCrypto();
            var key = aes.GenerateKey(256);
            var iv = aes.GenerateIV();

            Console.WriteLine("✓ 创建AES-256加密服务");
            Console.WriteLine($"✓ 生成密钥长度: {key?.Length * 8}位");
            Console.WriteLine($"✓ 生成IV长度: {iv?.Length * 8}位");

            var originalText = "这是需要加密的重要数据！";
            Console.WriteLine($"📝 原始数据: {originalText}");

            // 加密
            var encrypted = aes.Encrypt(originalText, key, iv);
            Console.WriteLine($"🔐 加密完成，密文长度: {encrypted.Length} 字节");

            // 解密
            var decrypted = aes.Decrypt(encrypted, key, iv);
            Console.WriteLine($"🔓 解密完成: {decrypted}");

            // 验证
            var isValid = originalText == decrypted;
            Console.WriteLine($"✅ 数据完整性验证: {(isValid ? "通过" : "失败")}");

            // 清理
            Array.Clear(key, 0, key.Length);
            Array.Clear(iv, 0, iv.Length);
            Console.WriteLine("🧹 敏感数据已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 对称加密演示失败: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 非对称加密演示
    /// </summary>
    static Task AsymmetricEncryptionDemo()
    {
        try
        {
            var rsa = new RsaAsymmetricCrypto();
            var keyPair = rsa.GenerateKeyPair(2048);

            Console.WriteLine("✓ 创建RSA加密服务");
            Console.WriteLine($"✓ 生成RSA-2048密钥对");
            Console.WriteLine($"  • 公钥长度: {keyPair.PublicKey.Length} 字节");
            Console.WriteLine($"  • 私钥长度: {keyPair.PrivateKey.Length} 字节");

            var originalText = "RSA非对称加密测试数据";
            Console.WriteLine($"📝 原始数据: {originalText}");

            // 公钥加密
            var encrypted = rsa.EncryptWithPublicKey(originalText, keyPair.PublicKey);
            Console.WriteLine($"🔐 公钥加密完成，密文长度: {encrypted.Length} 字节");

            // 私钥解密
            var decrypted = rsa.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey);
            Console.WriteLine($"🔓 私钥解密完成: {decrypted}");

            // 验证
            var isValid = originalText == decrypted;
            Console.WriteLine($"✅ 数据完整性验证: {(isValid ? "通过" : "失败")}");

            // 清理
            keyPair.ClearPrivateKey();
            Console.WriteLine("🧹 私钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 非对称加密演示失败: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 哈希算法演示
    /// </summary>
    static Task HashingDemo()
    {
        try
        {
            var hashProvider = new Sha256HashProvider();
            Console.WriteLine("✓ 创建SHA-256哈希提供者");

            var testData = "这是需要计算哈希的数据";
            Console.WriteLine($"📝 测试数据: {testData}");

            // 计算哈希
            var hash = hashProvider.ComputeHash(testData);
            Console.WriteLine($"🔍 SHA-256哈希: {hash}");

            // 验证哈希
            var isValid = hashProvider.VerifyHash(testData, hash);
            Console.WriteLine($"✅ 哈希验证: {(isValid ? "通过" : "失败")}");

            // Argon2密码哈希演示
            var passwordHasher = new Argon2PasswordHasher();
            var password = "TestPassword123!";
            Console.WriteLine($"\n🔐 Argon2密码哈希演示");
            Console.WriteLine($"📝 测试密码: {password}");

            var passwordHashResult = passwordHasher.HashPassword(password);
            Console.WriteLine($"🔐 Argon2哈希: {passwordHashResult.FormattedHash[..50]}...");

            var passwordValid = passwordHasher.VerifyPassword(password, passwordHashResult);
            Console.WriteLine($"✅ 密码验证: {(passwordValid ? "通过" : "失败")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 哈希算法演示失败: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 数字签名演示
    /// </summary>
    static Task DigitalSignatureDemo()
    {
        try
        {
            var rsaSignature = new RsaDigitalSignature();
            var rsaCrypto = new RsaAsymmetricCrypto();
            var keyPair = rsaCrypto.GenerateKeyPair(2048);

            Console.WriteLine("✓ 创建RSA数字签名服务");

            var document = "重要文档内容，需要数字签名保护";
            Console.WriteLine($"📄 文档内容: {document}");

            // 创建签名
            var signature = rsaSignature.Sign(document, keyPair.PrivateKey);
            Console.WriteLine($"✍️ 数字签名创建完成，签名长度: {signature.Length} 字节");

            // 验证签名
            var isValid = rsaSignature.Verify(document, signature, keyPair.PublicKey);
            Console.WriteLine($"✅ 签名验证: {(isValid ? "通过" : "失败")}");

            // 篡改检测
            var tamperedDocument = document + " [已篡改]";
            var isTamperedValid = rsaSignature.Verify(tamperedDocument, signature, keyPair.PublicKey);
            Console.WriteLine($"🚨 篡改检测: {(!isTamperedValid ? "✓ 检测到篡改" : "✗ 未检测到篡改")}");

            // 清理
            keyPair.ClearPrivateKey();
            Console.WriteLine("🧹 密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 数字签名演示失败: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 扩展方法演示
    /// </summary>
    static Task ExtensionMethodsDemo()
    {
        try
        {
            Console.WriteLine("✓ 扩展方法简化加密操作演示");

            // AES扩展方法
            var aes = new AesSymmetricCrypto();
            var key = aes.GenerateKey(256);

            var plainText = "使用扩展方法的加密测试";
            Console.WriteLine($"📝 原始文本: {plainText}");

            // 使用扩展方法加密
            var encrypted = plainText.EncryptAes(key);
            Console.WriteLine($"🔐 扩展方法加密完成");

            // 使用扩展方法解密
            var decrypted = encrypted.DecryptAes(key);
            Console.WriteLine($"🔓 扩展方法解密完成: {decrypted}");

            var isValid = plainText == decrypted;
            Console.WriteLine($"✅ 操作验证: {(isValid ? "成功" : "失败")}");

            // SHA-256扩展方法
            var testString = "扩展方法哈希测试";
            var hash = testString.ToSha256Hash();
            Console.WriteLine($"\n🔍 SHA-256扩展方法: {hash[..32]}...");

            // Argon2扩展方法
            var password = "ExtensionPassword123!";
            var argon2Hash = password.ToArgon2Hash();
            Console.WriteLine($"🔐 Argon2扩展方法: {argon2Hash.FormattedHash[..50]}...");

            var passwordValid = password.VerifyArgon2Hash(argon2Hash);
            Console.WriteLine($"✅ 密码验证: {(passwordValid ? "通过" : "失败")}");

            // 清理
            Array.Clear(key, 0, key.Length);
            Console.WriteLine("🧹 密钥已清理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 扩展方法演示失败: {ex.Message}");
        }

        return Task.CompletedTask;
    }
}
