# Liam.Cryptography 示例应用程序

## 项目概述

这是一个完整的 **Liam.Cryptography** 功能库示例应用程序，展示了该加密库的所有核心功能和最佳实践。通过运行这个控制台应用程序，您可以深入了解如何在实际项目中使用 Liam.Cryptography 库。

## 功能演示

### 🔐 1. 对称加密演示 (SymmetricEncryptionExamples)
- **AES-256基础加密/解密**：展示标准的AES加密流程
- **扩展方法使用**：演示如何使用字符串扩展方法简化操作
- **异步操作**：展示异步加密/解密的性能优势
- **异常处理**：演示各种异常情况的正确处理方法

### 🔑 2. 非对称加密演示 (AsymmetricEncryptionExamples)
- **RSA密钥对生成**：展示不同密钥长度的生成和使用
- **公钥加密/私钥解密**：演示RSA加密的完整流程
- **密钥管理**：展示密钥的导入导出功能
- **性能测试**：对比不同密钥长度的性能差异

### 🔍 3. 哈希算法演示 (HashingExamples)
- **SHA-256哈希计算**：展示基础哈希功能和验证
- **文件哈希**：演示大文件的哈希计算和完整性验证
- **Argon2密码哈希**：展示现代密码哈希算法的使用
- **性能对比**：对比不同哈希算法的性能表现

### ✍️ 4. 数字签名演示 (DigitalSignatureExamples)
- **RSA数字签名**：展示RSA-PSS签名的创建和验证
- **ECDSA数字签名**：展示Ed25519签名算法的使用
- **批量签名验证**：演示高效的批量签名处理
- **安全性验证**：展示数字签名的防篡改能力

### 🗝️ 5. 密钥管理演示 (KeyManagementExamples)
- **密钥生成**：展示对称和非对称密钥的安全生成
- **密钥导入导出**：演示密钥的安全存储和恢复
- **PBKDF2密钥派生**：展示基于密码的密钥派生
- **安全随机数生成**：演示密码学安全的随机数生成

### 🚀 6. 现代加密算法演示 (ModernCryptographyExamples)
- **ChaCha20-Poly1305 AEAD加密**：展示认证加密的使用
- **Ed25519数字签名**：展示现代椭圆曲线签名算法
- **Argon2密码哈希**：展示抗GPU攻击的密码哈希
- **性能基准测试**：对比现代算法与传统算法的性能

### 📁 7. 流式处理演示 (StreamProcessingExamples)
- **大文件加密**：展示内存高效的大文件处理
- **流式哈希计算**：演示大文件的流式哈希计算
- **异步流处理**：展示并发流式操作的性能优势
- **内存优化**：演示如何在处理大文件时保持低内存使用

### ⭐ 8. 最佳实践演示 (BestPracticesExamples)
- **密钥安全管理**：展示密钥的安全生成、存储和销毁
- **异常处理**：演示正确的异常处理和错误恢复
- **性能优化**：展示提高加密操作性能的技巧
- **安全编程**：展示安全的加密编程模式
- **常见陷阱避免**：展示常见安全陷阱和正确的避免方法

## 快速开始

### 环境要求
- **.NET 8.0** 或更高版本
- **Liam.Cryptography 1.1.0** NuGet包

### 运行示例

1. **克隆或下载项目**
   ```bash
   git clone https://gitee.com/liam-gitee/liam.git
   cd liam/examples/Liam.Cryptography.Example
   ```

2. **还原依赖包**
   ```bash
   dotnet restore
   ```

3. **运行示例程序**
   ```bash
   dotnet run
   ```

### 运行特定演示

如果您只想查看特定功能的演示，可以修改 `Program.cs` 文件，注释掉不需要的演示部分：

```csharp
// 只运行对称加密演示
await SymmetricEncryptionExamples.RunAllExamples();

// 只运行现代加密算法演示
await ModernCryptographyExamples.RunAllExamples();
```

## 项目结构

```
Liam.Cryptography.Example/
├── Program.cs                          # 主程序入口
├── Examples/                           # 功能演示目录
│   ├── SymmetricEncryptionExamples.cs  # 对称加密演示
│   ├── AsymmetricEncryptionExamples.cs # 非对称加密演示
│   ├── HashingExamples.cs              # 哈希算法演示
│   ├── DigitalSignatureExamples.cs     # 数字签名演示
│   ├── KeyManagementExamples.cs        # 密钥管理演示
│   ├── ModernCryptographyExamples.cs   # 现代加密算法演示
│   ├── StreamProcessingExamples.cs     # 流式处理演示
│   └── BestPracticesExamples.cs        # 最佳实践演示
├── Liam.Cryptography.Example.csproj    # 项目文件
└── README.md                           # 本文档
```

## 示例输出

运行程序后，您将看到类似以下的输出：

```
=== Liam.Cryptography 功能库示例程序 ===
本程序演示Liam.Cryptography库的所有核心功能

🔐 1. 对称加密演示
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 对称加密功能演示包括：
   • AES-256基础加密/解密
   • 使用扩展方法简化操作
   • 异步加密/解密操作
   • 异常处理最佳实践

🔐 基础AES-256加密演示
─────────────────────────
✓ 创建AES加密服务实例
✓ 生成AES-256密钥 (长度: 256位)
✓ 生成初始化向量 (长度: 128位)
📝 原始数据: 这是一段需要加密保护的重要数据！🔒
🔐 加密完成，密文长度: 64 字节
🔓 解密完成: 这是一段需要加密保护的重要数据！🔒
✅ 数据完整性验证: 通过
🧹 敏感数据已安全清理
...
```

## 学习建议

### 初学者路径
1. **从对称加密开始**：理解基础的加密/解密概念
2. **学习密钥管理**：了解密钥的安全生成和管理
3. **掌握哈希算法**：学习数据完整性验证
4. **探索数字签名**：理解身份验证和不可否认性

### 进阶用户路径
1. **现代加密算法**：学习ChaCha20-Poly1305和Ed25519
2. **流式处理**：掌握大文件的高效处理方法
3. **性能优化**：学习提高加密操作性能的技巧
4. **最佳实践**：掌握安全编程和常见陷阱避免

## 实际应用场景

### 🔐 数据保护
- **用户敏感信息加密**：使用AES-256保护用户数据
- **文件加密存储**：使用流式加密保护大文件
- **数据库字段加密**：保护数据库中的敏感字段

### 🔑 身份认证
- **密码安全存储**：使用Argon2哈希存储用户密码
- **API密钥管理**：安全生成和管理API密钥
- **数字证书**：使用RSA或ECDSA进行身份验证

### 📁 文件完整性
- **文件哈希验证**：使用SHA-256验证文件完整性
- **数字签名**：为文件添加数字签名防止篡改
- **版本控制**：验证软件包的完整性和来源

### 🌐 网络通信
- **HTTPS增强**：使用现代加密算法增强安全性
- **消息加密**：保护网络传输中的敏感消息
- **端到端加密**：实现客户端到服务器的端到端加密

## 性能参考

在现代硬件上的典型性能表现：

| 操作类型 | 算法 | 数据大小 | 性能 |
|---------|------|----------|------|
| 对称加密 | AES-256 | 1MB | ~50MB/s |
| 对称加密 | ChaCha20-Poly1305 | 1MB | ~80MB/s |
| 非对称加密 | RSA-2048 | 1KB | ~1000 ops/s |
| 数字签名 | Ed25519 | 1KB | ~10000 ops/s |
| 哈希计算 | SHA-256 | 1MB | ~200MB/s |
| 密码哈希 | Argon2 | 1个密码 | ~100ms |

*注：实际性能取决于硬件配置和具体使用场景*

## 安全建议

### 🔒 密钥管理
- 使用256位密钥长度（AES-256）
- 每次加密使用新的随机IV
- 及时清理内存中的敏感数据
- 定期轮换密钥

### 🛡️ 算法选择
- **对称加密**：优先选择ChaCha20-Poly1305（AEAD）
- **非对称加密**：新项目使用Ed25519，兼容性要求使用RSA-2048+
- **密码哈希**：使用Argon2id替代PBKDF2
- **数据哈希**：使用SHA-256或更高强度算法

### ⚠️ 常见陷阱
- 不要硬编码密钥在源代码中
- 不要重用IV或Nonce
- 不要使用弱随机数生成器
- 不要忽略异常处理和错误恢复

## 技术支持

- **NuGet包**：https://www.nuget.org/packages/Liam.Cryptography/
- **源代码**：https://gitee.com/liam-gitee/liam
- **问题反馈**：请在Gitee上提交Issue
- **文档**：查看库的README.md获取详细API文档

## 许可证

本示例项目采用MIT许可证，与Liam.Cryptography库保持一致。

---

**开始探索Liam.Cryptography的强大功能，构建更安全的应用程序！** 🚀
